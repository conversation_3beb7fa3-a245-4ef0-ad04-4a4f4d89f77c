{"name": "@darajat/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@angular-devkit/build-angular": "~20.1.0", "@angular/cdk": "^20.1.2", "@angular/common": "~20.1.0", "@angular/compiler": "~20.1.0", "@angular/core": "~20.1.0", "@angular/forms": "~20.1.0", "@angular/platform-browser": "~20.1.0", "@angular/platform-browser-dynamic": "~20.1.0", "@angular/platform-server": "~20.1.0", "@angular/router": "~20.1.0", "@angular/ssr": "~20.1.0", "express": "^4.21.2", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primeng": "^20.0.0", "rxjs": "~7.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/core": "~20.1.0", "@angular-devkit/schematics": "~20.1.0", "@angular/build": "~20.1.0", "@angular/cli": "~20.1.0", "@angular/compiler-cli": "~20.1.0", "@angular/language-service": "~20.1.0", "@eslint/js": "^9.8.0", "@nx/angular": "21.3.1", "@nx/devkit": "21.3.1", "@nx/eslint": "21.3.1", "@nx/eslint-plugin": "21.3.1", "@nx/jest": "21.3.1", "@nx/js": "21.3.1", "@nx/playwright": "21.3.1", "@nx/web": "21.3.1", "@nx/workspace": "21.3.1", "@playwright/test": "^1.36.0", "@schematics/angular": "~20.1.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/node": "18.16.9", "@typescript-eslint/utils": "^8.29.0", "angular-eslint": "^20.0.0", "autoprefixer": "^10.4.0", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-playwright": "^1.6.2", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jest-preset-angular": "~14.6.0", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "~20.1.0", "nx": "21.3.1", "postcss": "^8.4.5", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "verdaccio": "^6.0.5"}, "nx": {"includedScripts": []}}