{"name": "parents", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "parents/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/parents", "browser": "parents/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "parents/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "parents/public"}], "styles": ["parents/src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "parents:build:production"}, "development": {"buildTarget": "parents:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "parents:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "parents/jest.config.ts"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "parents:build", "port": 4200, "staticFilePath": "dist/parents/browser", "spa": true}}}}