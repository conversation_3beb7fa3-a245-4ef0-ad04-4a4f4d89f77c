.stats-card {
  height: 100%;
  transition: all 0.2s ease-in-out;
  border: 1px solid #E0E0E0;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  .stats-content {
    display: flex;
    gap: 1rem;
    padding: 0.5rem;

    .stats-icon {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      min-width: 48px;

      i {
        font-size: 2rem;
        opacity: 0.8;
      }
    }

    .stats-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .stats-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .stats-title {
          margin: 0;
          font-size: 0.875rem;
          font-weight: 500;
          color: #757575;
          line-height: 1.2;
        }

        .stats-actions {
          display: flex;
          gap: 0.25rem;
          opacity: 0;
          transition: opacity 0.2s ease-in-out;
        }
      }

      .stats-value-section {
        display: flex;
        align-items: baseline;
        gap: 0.75rem;

        .stats-value {
          font-size: 2rem;
          font-weight: 700;
          color: #212121;
          line-height: 1;
        }

        .stats-trend {
          :host ::ng-deep {
            .p-tag {
              font-size: 0.75rem;
              padding: 0.25rem 0.5rem;
              
              .p-tag-icon {
                font-size: 0.625rem;
              }
            }
          }
        }
      }

      .stats-subtitle {
        font-size: 0.75rem;
        color: #757575;
        margin: 0;
      }

      .stats-progress {
        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.5rem;

          .progress-label {
            font-size: 0.75rem;
            color: #757575;
          }

          .progress-value {
            font-size: 0.75rem;
            font-weight: 600;
            color: #212121;
          }
        }
      }
    }
  }

  // Hover effect for actions
  &:hover {
    .stats-actions {
      opacity: 1;
    }
  }
}

// Size variations
.stats-card {
  &.small {
    .stats-content {
      padding: 0.75rem;

      .stats-icon i {
        font-size: 1.5rem;
      }

      .stats-main {
        .stats-value-section .stats-value {
          font-size: 1.5rem;
        }
      }
    }
  }

  &.large {
    .stats-content {
      padding: 1.5rem;

      .stats-icon i {
        font-size: 2.5rem;
      }

      .stats-main {
        .stats-value-section .stats-value {
          font-size: 2.5rem;
        }
      }
    }
  }
}

// Variant styles
.stats-card {
  &.outlined {
    border: 2px solid #007ACC;
    background: #FFFFFF;
  }

  &.filled {
    background: linear-gradient(135deg, #007ACC 0%, #005A9E 100%);
    color: #FFFFFF;
    border: none;

    .stats-content .stats-main {
      .stats-header .stats-title {
        color: rgba(255, 255, 255, 0.8);
      }

      .stats-value-section .stats-value {
        color: #FFFFFF;
      }

      .stats-subtitle {
        color: rgba(255, 255, 255, 0.7);
      }

      .stats-progress {
        .progress-header {
          .progress-label {
            color: rgba(255, 255, 255, 0.8);
          }

          .progress-value {
            color: #FFFFFF;
          }
        }
      }
    }

    .stats-icon i {
      color: #FFFFFF !important;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .stats-card {
    .stats-content {
      .stats-icon {
        min-width: 40px;

        i {
          font-size: 1.5rem;
        }
      }

      .stats-main {
        .stats-value-section {
          flex-direction: column;
          gap: 0.5rem;
          align-items: flex-start;

          .stats-value {
            font-size: 1.75rem;
          }
        }
      }
    }
  }
}

// Animation
.stats-card {
  .stats-value {
    animation: countUp 0.8s ease-out;
  }
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Custom progress bar styling
:host ::ng-deep {
  .p-progressbar {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;

    .p-progressbar-value {
      border-radius: 3px;
      transition: width 0.6s ease-in-out;
    }
  }

  // Filled variant progress bar
  .stats-card.filled {
    .p-progressbar {
      background: rgba(255, 255, 255, 0.2);

      .p-progressbar-value {
        background: #FFFFFF !important;
      }
    }
  }
}

// Loading state
.stats-card.loading {
  .stats-value {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    height: 2rem;
    width: 60%;
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
