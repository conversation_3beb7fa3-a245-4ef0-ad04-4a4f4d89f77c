import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ProgressBarModule } from 'primeng/progressbar';
import { TagModule } from 'primeng/tag';
import { ButtonModule } from 'primeng/button';

export interface StatCardData {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: string;
  iconColor?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  progress?: {
    value: number;
    max?: number;
    color?: string;
  };
  actions?: {
    label: string;
    icon?: string;
    action: () => void;
  }[];
}

@Component({
  selector: 'darajat-stats-card',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ProgressBarModule,
    TagModule,
    ButtonModule,
  ],
  template: `
    <p-card class="stats-card" [ngClass]="cardClass">
      <ng-template pTemplate="content">
        <div class="stats-content">
          <!-- Icon Section -->
          <div class="stats-icon" *ngIf="data.icon">
            <i 
              [class]="'pi ' + data.icon" 
              [style.color]="data.iconColor || '#007ACC'"
            ></i>
          </div>

          <!-- Main Content -->
          <div class="stats-main">
            <div class="stats-header">
              <h3 class="stats-title">{{ data.title }}</h3>
              <div class="stats-actions" *ngIf="data.actions && data.actions.length > 0">
                <button
                  *ngFor="let action of data.actions"
                  pButton
                  type="button"
                  [icon]="action.icon || 'pi pi-ellipsis-v'"
                  class="p-button-text p-button-sm"
                  (click)="action.action()"
                  [pTooltip]="action.label"
                ></button>
              </div>
            </div>

            <div class="stats-value-section">
              <div class="stats-value">{{ formatValue(data.value) }}</div>
              <div class="stats-trend" *ngIf="data.trend">
                <p-tag
                  [value]="formatTrend(data.trend)"
                  [severity]="data.trend.isPositive ? 'success' : 'danger'"
                  [icon]="data.trend.isPositive ? 'pi pi-arrow-up' : 'pi pi-arrow-down'"
                ></p-tag>
              </div>
            </div>

            <div class="stats-subtitle" *ngIf="data.subtitle">
              {{ data.subtitle }}
            </div>

            <!-- Progress Bar -->
            <div class="stats-progress" *ngIf="data.progress">
              <div class="progress-header">
                <span class="progress-label">Progress</span>
                <span class="progress-value">
                  {{ data.progress.value }}{{ data.progress.max ? '/' + data.progress.max : '%' }}
                </span>
              </div>
              <p-progressBar
                [value]="getProgressPercentage()"
                [style]="{ height: '6px' }"
                [color]="data.progress.color || '#007ACC'"
              ></p-progressBar>
            </div>
          </div>
        </div>
      </ng-template>
    </p-card>
  `,
  styleUrls: ['./stats-card.component.scss']
})
export class StatsCardComponent {
  @Input() data: StatCardData = {
    title: 'Statistic',
    value: 0
  };
  @Input() cardClass = '';
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() variant: 'default' | 'outlined' | 'filled' = 'default';

  formatValue(value: string | number): string {
    if (typeof value === 'number') {
      // Format large numbers with K, M, B suffixes
      if (value >= 1000000000) {
        return (value / 1000000000).toFixed(1) + 'B';
      } else if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M';
      } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'K';
      }
      return value.toLocaleString();
    }
    return value.toString();
  }

  formatTrend(trend: { value: number; isPositive: boolean; label?: string }): string {
    const sign = trend.isPositive ? '+' : '';
    const suffix = trend.label || '%';
    return `${sign}${trend.value}${suffix}`;
  }

  getProgressPercentage(): number {
    if (!this.data.progress) return 0;
    
    if (this.data.progress.max) {
      return (this.data.progress.value / this.data.progress.max) * 100;
    }
    
    return this.data.progress.value;
  }
}
