.time-period-selector {
  background: #FFFFFF;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .period-tabs {
    :host ::ng-deep {
      .p-tabview-nav {
        background: #F5F5F5;
        border-bottom: 1px solid #E0E0E0;
        margin: 0;

        .p-tabview-nav-link {
          background: transparent;
          border: none;
          color: #757575;
          padding: 1rem 1.5rem;
          font-weight: 500;
          transition: all 0.2s ease-in-out;

          &:hover {
            background: #E3F2FD;
            color: #007ACC;
          }
        }

        .p-highlight .p-tabview-nav-link {
          background: #FFFFFF;
          color: #007ACC;
          border-bottom: 2px solid #007ACC;
        }
      }

      .p-tabview-panels {
        background: #FFFFFF;
        border: none;
        padding: 0;

        .p-tabview-panel {
          padding: 1.5rem;
        }
      }
    }
  }

  .quick-periods {
    .period-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      margin-bottom: 1rem;

      button {
        min-width: 100px;
        transition: all 0.2s ease-in-out;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }

    .custom-dropdown {
      .custom-period-dropdown {
        min-width: 200px;
      }
    }
  }

  .custom-range {
    .date-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .date-input-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #212121;
        }

        :host ::ng-deep {
          .p-calendar {
            width: 100%;

            .p-inputtext {
              width: 100%;
            }
          }
        }
      }
    }

    .range-actions {
      display: flex;
      gap: 0.75rem;
      justify-content: flex-end;
    }
  }

  .comparison-options {
    .comparison-info {
      margin-bottom: 1rem;

      p {
        margin: 0;
        color: #757575;
        font-size: 0.875rem;
      }
    }

    .comparison-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      margin-bottom: 1rem;

      button {
        min-width: 140px;
      }
    }

    .comparison-custom {
      padding-top: 1rem;
      border-top: 1px solid #E0E0E0;

      .date-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;

        .date-input-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;

          label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #212121;
          }
        }
      }
    }
  }

  .selected-period {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: #F8F9FA;
    border-top: 1px solid #E0E0E0;

    .period-info {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;

      .period-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #212121;
      }

      .period-range {
        font-size: 0.75rem;
        color: #757575;
      }
    }

    .period-actions {
      display: flex;
      gap: 0.5rem;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .time-period-selector {
    .quick-periods {
      .period-buttons {
        button {
          min-width: 80px;
          font-size: 0.8rem;
        }
      }
    }

    .custom-range {
      .date-inputs {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .range-actions {
        justify-content: stretch;

        button {
          flex: 1;
        }
      }
    }

    .comparison-options {
      .comparison-buttons {
        button {
          min-width: 120px;
          font-size: 0.8rem;
        }
      }

      .comparison-custom {
        .date-inputs {
          grid-template-columns: 1fr;
        }
      }
    }

    .selected-period {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;

      .period-info {
        text-align: center;
      }

      .period-actions {
        justify-content: center;
      }
    }
  }
}

@media (max-width: 576px) {
  .time-period-selector {
    .period-tabs {
      :host ::ng-deep {
        .p-tabview-nav {
          .p-tabview-nav-link {
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
          }
        }

        .p-tabview-panels {
          .p-tabview-panel {
            padding: 1rem;
          }
        }
      }
    }

    .quick-periods {
      .period-buttons {
        gap: 0.5rem;

        button {
          min-width: 70px;
          font-size: 0.75rem;
          padding: 0.5rem 0.75rem;
        }
      }
    }

    .comparison-options {
      .comparison-buttons {
        flex-direction: column;

        button {
          min-width: auto;
          width: 100%;
        }
      }
    }
  }
}

// Animation
.time-period-selector {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Button hover effects
.period-buttons button,
.comparison-buttons button {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}
