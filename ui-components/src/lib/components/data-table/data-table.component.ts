import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { TagModule } from 'primeng/tag';
import { MenuModule } from 'primeng/menu';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { MenuItem } from 'primeng/api';

export interface TableColumn {
  field: string;
  header: string;
  sortable?: boolean;
  filterable?: boolean;
  type?: 'text' | 'number' | 'date' | 'currency' | 'status' | 'actions';
  width?: string;
  align?: 'left' | 'center' | 'right';
  format?: (value: any) => string;
  statusConfig?: {
    [key: string]: {
      severity: 'success' | 'info' | 'warning' | 'danger' | 'secondary';
      label?: string;
    };
  };
}

export interface TableAction {
  label: string;
  icon?: string;
  command: (rowData: any) => void;
  visible?: (rowData: any) => boolean;
  disabled?: (rowData: any) => boolean;
}

@Component({
  selector: 'darajat-data-table',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DropdownModule,
    TagModule,
    MenuModule,
    OverlayPanelModule,
  ],
  template: `
    <div class="data-table-container">
      <!-- Table Header -->
      <div class="table-header" *ngIf="showHeader">
        <div class="table-title-section">
          <h3 class="table-title" *ngIf="title">{{ title }}</h3>
          <p class="table-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        </div>
        
        <div class="table-actions">
          <!-- Global Search -->
          <div class="search-container" *ngIf="globalFilterFields.length > 0">
            <span class="p-input-icon-left">
              <i class="pi pi-search"></i>
              <input
                pInputText
                type="text"
                placeholder="Search..."
                (input)="onGlobalFilter($event)"
                class="global-search"
              />
            </span>
          </div>

          <!-- Custom Actions -->
          <ng-content select="[slot=actions]"></ng-content>

          <!-- Export Button -->
          <button
            *ngIf="showExport"
            pButton
            type="button"
            icon="pi pi-download"
            class="p-button-outlined"
            (click)="onExport()"
            pTooltip="Export data"
          ></button>
        </div>
      </div>

      <!-- Data Table -->
      <p-table
        #dt
        [value]="data"
        [columns]="columns"
        [paginator]="paginator"
        [rows]="rows"
        [rowsPerPageOptions]="rowsPerPageOptions"
        [loading]="loading"
        [sortMode]="sortMode"
        [sortField]="sortField"
        [sortOrder]="sortOrder"
        [globalFilterFields]="globalFilterFields"
        [scrollable]="scrollable"
        [scrollHeight]="scrollHeight"
        [responsive]="responsive"
        [selection]="selection"
        [selectionMode]="selectionMode"
        (selectionChange)="onSelectionChange($event)"
        (sortFunction)="onSort($event)"
        styleClass="p-datatable-sm"
        [tableStyle]="{ 'min-width': '50rem' }"
      >
        <!-- Table Header -->
        <ng-template pTemplate="header" let-columns>
          <tr>
            <th *ngIf="selectionMode" style="width: 3rem">
              <p-tableHeaderCheckbox *ngIf="selectionMode === 'multiple'"></p-tableHeaderCheckbox>
            </th>
            <th
              *ngFor="let col of columns"
              [pSortableColumn]="col.sortable ? col.field : null"
              [style.width]="col.width"
              [style.text-align]="col.align || 'left'"
            >
              {{ col.header }}
              <p-sortIcon *ngIf="col.sortable" [field]="col.field"></p-sortIcon>
            </th>
            <th *ngIf="actions.length > 0" style="width: 4rem; text-align: center">
              Actions
            </th>
          </tr>
        </ng-template>

        <!-- Table Body -->
        <ng-template pTemplate="body" let-rowData let-rowIndex="rowIndex" let-columns="columns">
          <tr [class.selected-row]="isRowSelected(rowData)">
            <td *ngIf="selectionMode">
              <p-tableCheckbox [value]="rowData" *ngIf="selectionMode === 'multiple'"></p-tableCheckbox>
              <p-tableRadioButton [value]="rowData" *ngIf="selectionMode === 'single'"></p-tableRadioButton>
            </td>
            <td
              *ngFor="let col of columns"
              [style.text-align]="col.align || 'left'"
            >
              <!-- Text/Default -->
              <span *ngIf="!col.type || col.type === 'text'">
                {{ col.format ? col.format(getFieldValue(rowData, col.field)) : getFieldValue(rowData, col.field) }}
              </span>

              <!-- Number -->
              <span *ngIf="col.type === 'number'" class="number-cell">
                {{ formatNumber(getFieldValue(rowData, col.field)) }}
              </span>

              <!-- Currency -->
              <span *ngIf="col.type === 'currency'" class="currency-cell">
                {{ formatCurrency(getFieldValue(rowData, col.field)) }}
              </span>

              <!-- Date -->
              <span *ngIf="col.type === 'date'" class="date-cell">
                {{ formatDate(getFieldValue(rowData, col.field)) }}
              </span>

              <!-- Status -->
              <p-tag
                *ngIf="col.type === 'status'"
                [value]="getStatusLabel(getFieldValue(rowData, col.field), col)"
                [severity]="getStatusSeverity(getFieldValue(rowData, col.field), col)"
              ></p-tag>
            </td>

            <!-- Actions Column -->
            <td *ngIf="actions.length > 0" class="actions-cell">
              <button
                pButton
                type="button"
                icon="pi pi-ellipsis-v"
                class="p-button-text p-button-sm"
                (click)="showActionsMenu($event, rowData)"
              ></button>
            </td>
          </tr>
        </ng-template>

        <!-- Empty State -->
        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="getColspan()" class="empty-message">
              <div class="empty-state">
                <i class="pi pi-inbox empty-icon"></i>
                <p>{{ emptyMessage }}</p>
              </div>
            </td>
          </tr>
        </ng-template>

        <!-- Loading Template -->
        <ng-template pTemplate="loadingbody">
          <tr>
            <td [attr.colspan]="getColspan()" class="loading-message">
              <div class="loading-state">
                <i class="pi pi-spin pi-spinner loading-icon"></i>
                <p>Loading data...</p>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>

      <!-- Actions Menu -->
      <p-overlayPanel #actionsMenu>
        <p-menu [model]="currentRowActions" [popup]="false"></p-menu>
      </p-overlayPanel>
    </div>
  `,
  styleUrls: ['./data-table.component.scss']
})
export class DataTableComponent implements OnInit {
  @Input() title = '';
  @Input() subtitle = '';
  @Input() data: any[] = [];
  @Input() columns: TableColumn[] = [];
  @Input() actions: TableAction[] = [];
  @Input() loading = false;
  @Input() paginator = true;
  @Input() rows = 10;
  @Input() rowsPerPageOptions = [5, 10, 20, 50];
  @Input() sortMode: 'single' | 'multiple' = 'single';
  @Input() sortField = '';
  @Input() sortOrder = 1;
  @Input() globalFilterFields: string[] = [];
  @Input() scrollable = false;
  @Input() scrollHeight = '400px';
  @Input() responsive = true;
  @Input() selectionMode: 'single' | 'multiple' | null = null;
  @Input() selection: any | any[] = null;
  @Input() showHeader = true;
  @Input() showExport = true;
  @Input() emptyMessage = 'No data found';

  @Output() selectionChange = new EventEmitter<any>();
  @Output() sort = new EventEmitter<any>();
  @Output() export = new EventEmitter<void>();

  currentRowActions: MenuItem[] = [];

  ngOnInit(): void {
    // Initialize component
  }

  onGlobalFilter(event: Event): void {
    const target = event.target as HTMLInputElement;
    // Implement global filtering logic
  }

  onSelectionChange(selection: any): void {
    this.selection = selection;
    this.selectionChange.emit(selection);
  }

  onSort(event: any): void {
    this.sort.emit(event);
  }

  onExport(): void {
    this.export.emit();
  }

  showActionsMenu(event: Event, rowData: any): void {
    this.currentRowActions = this.actions
      .filter(action => !action.visible || action.visible(rowData))
      .map(action => ({
        label: action.label,
        icon: action.icon,
        disabled: action.disabled ? action.disabled(rowData) : false,
        command: () => action.command(rowData)
      }));
  }

  getFieldValue(obj: any, field: string): any {
    return field.split('.').reduce((o, f) => o && o[f], obj);
  }

  formatNumber(value: any): string {
    return typeof value === 'number' ? value.toLocaleString() : value;
  }

  formatCurrency(value: any): string {
    return typeof value === 'number' ? 
      new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value) : 
      value;
  }

  formatDate(value: any): string {
    return value ? new Date(value).toLocaleDateString() : '';
  }

  getStatusLabel(value: any, column: TableColumn): string {
    return column.statusConfig?.[value]?.label || value;
  }

  getStatusSeverity(value: any, column: TableColumn): any {
    return column.statusConfig?.[value]?.severity || 'info';
  }

  isRowSelected(rowData: any): boolean {
    if (!this.selection) return false;
    if (Array.isArray(this.selection)) {
      return this.selection.includes(rowData);
    }
    return this.selection === rowData;
  }

  getColspan(): number {
    let colspan = this.columns.length;
    if (this.selectionMode) colspan++;
    if (this.actions.length > 0) colspan++;
    return colspan;
  }
}
