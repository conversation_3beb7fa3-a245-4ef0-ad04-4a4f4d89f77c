{"name": "students", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "students/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/students", "browser": "students/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "students/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "students/public"}], "styles": ["students/src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "students:build:production"}, "development": {"buildTarget": "students:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "students:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "students/jest.config.ts"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "students:build", "port": 4200, "staticFilePath": "dist/students/browser", "spa": true}}}}