{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@darajat/shared-layout": ["shared-layout/src/index.ts"], "@darajat/shared-models": ["shared-models/src/index.ts"], "@darajat/shared-services": ["shared-services/src/index.ts"], "@darajat/shared-utils": ["shared-utils/src/index.ts"], "@darajat/ui-components": ["ui-components/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}