{"name": "shared-models", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "shared-models/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "shared-models/ng-package.json"}, "configurations": {"production": {"tsConfig": "shared-models/tsconfig.lib.prod.json"}, "development": {"tsConfig": "shared-models/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "shared-models/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}