{"name": "shared-layout", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "shared-layout/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "shared-layout/ng-package.json"}, "configurations": {"production": {"tsConfig": "shared-layout/tsconfig.lib.prod.json"}, "development": {"tsConfig": "shared-layout/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "shared-layout/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}