import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HeaderComponent } from '../header/header.component';
import { SidebarComponent, SidebarMenuItem } from '../sidebar/sidebar.component';

export interface LayoutConfig {
  title?: string;
  showSidebar?: boolean;
  sidebarExpanded?: boolean;
  menuItems?: SidebarMenuItem[];
  user?: {
    name: string;
    avatar?: string;
    initials?: string;
  };
  notifications?: {
    count: number;
  };
}

@Component({
  selector: 'darajat-main-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    HeaderComponent,
    SidebarComponent,
  ],
  template: `
    <div class="layout-container">
      <!-- Header -->
      <darajat-header
        [title]="config.title || 'Darajat'"
        [pageTitle]="pageTitle"
        [userName]="config.user?.name || 'User'"
        [userAvatar]="config.user?.avatar || ''"
        [userInitials]="config.user?.initials || 'U'"
        [notificationCount]="config.notifications?.count || 0"
        [searchQuery]="searchQuery"
        (menuToggle)="onMenuToggle()"
        (search)="onSearch($event)"
        (notificationClick)="onNotificationClick()"
        (profileAction)="onProfileAction($event)"
      ></darajat-header>

      <!-- Sidebar -->
      <darajat-sidebar
        *ngIf="config.showSidebar !== false"
        [isExpanded]="isSidebarExpanded"
        [activeItemId]="activeMenuItem"
        [menuItems]="config.menuItems || defaultMenuItems"
        (toggle)="onSidebarToggle($event)"
        (itemClick)="onMenuItemClick($event)"
      ></darajat-sidebar>

      <!-- Main Content -->
      <main 
        class="main-content"
        [class.sidebar-expanded]="isSidebarExpanded && config.showSidebar !== false"
        [class.no-sidebar]="config.showSidebar === false"
      >
        <div class="content-wrapper">
          <ng-content></ng-content>
        </div>
      </main>

      <!-- Mobile Overlay -->
      <div 
        *ngIf="isSidebarExpanded && isMobile"
        class="mobile-overlay"
        (click)="closeSidebar()"
      ></div>
    </div>
  `,
  styleUrls: ['./main-layout.component.scss']
})
export class MainLayoutComponent implements OnInit {
  @Input() config: LayoutConfig = {};
  @Input() pageTitle = 'Dashboard';
  @Input() activeMenuItem = 'dashboard';
  @Input() searchQuery = '';

  @Output() menuToggle = new EventEmitter<boolean>();
  @Output() search = new EventEmitter<string>();
  @Output() notificationClick = new EventEmitter<void>();
  @Output() profileAction = new EventEmitter<string>();
  @Output() menuItemClick = new EventEmitter<SidebarMenuItem>();

  isSidebarExpanded = false;
  isMobile = false;

  defaultMenuItems: SidebarMenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'pi-home',
      route: '/dashboard'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: 'pi-chart-line',
      route: '/analytics'
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: 'pi-envelope',
      route: '/messages',
      badge: 5,
      badgeClass: 'badge-danger'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: 'pi-file-pdf',
      route: '/reports'
    },
    {
      id: 'users',
      label: 'Users',
      icon: 'pi-users',
      route: '/users'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'pi-cog',
      route: '/settings'
    }
  ];

  ngOnInit(): void {
    this.isSidebarExpanded = this.config.sidebarExpanded ?? false;
    this.checkMobile();
    this.setupResizeListener();
  }

  private checkMobile(): void {
    this.isMobile = window.innerWidth < 992; // lg breakpoint
  }

  private setupResizeListener(): void {
    window.addEventListener('resize', () => {
      this.checkMobile();
      if (!this.isMobile && this.isSidebarExpanded) {
        // Auto-collapse on mobile to desktop transition
        this.isSidebarExpanded = this.config.sidebarExpanded ?? false;
      }
    });
  }

  onMenuToggle(): void {
    this.isSidebarExpanded = !this.isSidebarExpanded;
    this.menuToggle.emit(this.isSidebarExpanded);
  }

  onSidebarToggle(expanded: boolean): void {
    this.isSidebarExpanded = expanded;
    this.menuToggle.emit(expanded);
  }

  onSearch(query: string): void {
    this.searchQuery = query;
    this.search.emit(query);
  }

  onNotificationClick(): void {
    this.notificationClick.emit();
  }

  onProfileAction(action: string): void {
    this.profileAction.emit(action);
  }

  onMenuItemClick(item: SidebarMenuItem): void {
    this.activeMenuItem = item.id;
    this.menuItemClick.emit(item);
    
    // Auto-close sidebar on mobile after item click
    if (this.isMobile) {
      this.closeSidebar();
    }
  }

  closeSidebar(): void {
    this.isSidebarExpanded = false;
    this.menuToggle.emit(false);
  }
}
