{"extends": "../tsconfig.base.json", "compilerOptions": {"target": "es2022", "moduleResolution": "bundler", "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "module": "preserve"}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "typeCheckHostBindings": true, "strictTemplates": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}