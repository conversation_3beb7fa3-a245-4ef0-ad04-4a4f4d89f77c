{"name": "shared-services", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "shared-services/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "shared-services/ng-package.json"}, "configurations": {"production": {"tsConfig": "shared-services/tsconfig.lib.prod.json"}, "development": {"tsConfig": "shared-services/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "shared-services/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}