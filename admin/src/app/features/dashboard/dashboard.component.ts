import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  AnalyticsChartComponent, 
  StatsCardComponent, 
  DataTableComponent,
  TimePeriodSelectorComponent,
  StatCardData,
  ChartData,
  TableColumn,
  TableAction,
  TimePeriod
} from '@darajat/ui-components';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    AnalyticsChartComponent,
    StatsCardComponent,
    DataTableComponent,
    TimePeriodSelectorComponent,
  ],
  template: `
    <div class="dashboard-container">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Admin Dashboard</h1>
        <p class="page-subtitle">Welcome to the Darajat School Management System</p>
      </div>

      <!-- Time Period Selector -->
      <div class="time-period-section">
        <darajat-time-period-selector
          [selectedPeriod]="selectedPeriod"
          (periodChange)="onPeriodChange($event)"
          (refresh)="onRefresh()"
        ></darajat-time-period-selector>
      </div>

      <!-- Stats Cards Grid -->
      <div class="stats-grid">
        <darajat-stats-card
          *ngFor="let stat of statsData"
          [data]="stat"
          [size]="'medium'"
        ></darajat-stats-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <div class="chart-container">
          <darajat-analytics-chart
            title="Student Enrollment Trends"
            subtitle="Monthly enrollment data"
            [chartType]="'bar'"
            [chartData]="enrollmentChartData"
            [isLoading]="chartsLoading"
            [stats]="enrollmentStats"
          ></darajat-analytics-chart>
        </div>

        <div class="chart-container">
          <darajat-analytics-chart
            title="Academic Performance"
            subtitle="Average grades by subject"
            [chartType]="'line'"
            [chartData]="performanceChartData"
            [isLoading]="chartsLoading"
            [stats]="performanceStats"
          ></darajat-analytics-chart>
        </div>
      </div>

      <!-- Recent Activities Table -->
      <div class="table-section">
        <darajat-data-table
          title="Recent Activities"
          subtitle="Latest system activities and user actions"
          [data]="recentActivities"
          [columns]="activityColumns"
          [actions]="activityActions"
          [loading]="tableLoading"
          [rows]="10"
          [globalFilterFields]="['user', 'action', 'module']"
          (export)="onExportActivities()"
        ></darajat-data-table>
      </div>
    </div>
  `,
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  selectedPeriod: TimePeriod | null = null;
  chartsLoading = false;
  tableLoading = false;

  // Stats data
  statsData: StatCardData[] = [
    {
      title: 'Total Students',
      value: 1247,
      icon: 'pi-users',
      iconColor: '#007ACC',
      trend: {
        value: 12,
        isPositive: true,
        label: '%'
      },
      subtitle: 'Active students this semester'
    },
    {
      title: 'Total Teachers',
      value: 89,
      icon: 'pi-user',
      iconColor: '#4CAF50',
      trend: {
        value: 3,
        isPositive: true,
        label: '%'
      },
      subtitle: 'Faculty members'
    },
    {
      title: 'Active Classes',
      value: 156,
      icon: 'pi-book',
      iconColor: '#FF9800',
      progress: {
        value: 85,
        color: '#FF9800'
      },
      subtitle: 'Classes in session'
    },
    {
      title: 'System Usage',
      value: '94%',
      icon: 'pi-chart-line',
      iconColor: '#2196F3',
      trend: {
        value: 5,
        isPositive: true,
        label: '%'
      },
      subtitle: 'Platform utilization'
    }
  ];

  // Chart data
  enrollmentChartData: ChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'New Enrollments',
        data: [65, 59, 80, 81, 56, 55],
        backgroundColor: '#007ACC',
        borderColor: '#005A9E',
        borderWidth: 1
      },
      {
        label: 'Re-enrollments',
        data: [28, 48, 40, 19, 86, 27],
        backgroundColor: '#4CAF50',
        borderColor: '#388E3C',
        borderWidth: 1
      }
    ]
  };

  performanceChartData: ChartData = {
    labels: ['Math', 'Science', 'English', 'History', 'Art', 'PE'],
    datasets: [
      {
        label: 'Average Grade',
        data: [85, 78, 92, 88, 76, 89],
        backgroundColor: 'rgba(0, 122, 204, 0.2)',
        borderColor: '#007ACC',
        borderWidth: 2,
        fill: true
      }
    ]
  };

  enrollmentStats = [
    { label: 'Total Enrolled', value: '1,247', class: 'positive' },
    { label: 'Pending Applications', value: '23', class: 'neutral' },
    { label: 'Dropout Rate', value: '2.1%', class: 'negative' }
  ];

  performanceStats = [
    { label: 'Overall Average', value: '84.7%', class: 'positive' },
    { label: 'Improvement', value: '+3.2%', class: 'positive' },
    { label: 'Below Average', value: '12%', class: 'neutral' }
  ];

  // Table data
  activityColumns: TableColumn[] = [
    { field: 'timestamp', header: 'Time', type: 'date', sortable: true, width: '150px' },
    { field: 'user', header: 'User', sortable: true, filterable: true },
    { field: 'action', header: 'Action', sortable: true, filterable: true },
    { field: 'module', header: 'Module', sortable: true, filterable: true },
    { 
      field: 'status', 
      header: 'Status', 
      type: 'status',
      statusConfig: {
        'success': { severity: 'success', label: 'Success' },
        'warning': { severity: 'warning', label: 'Warning' },
        'error': { severity: 'danger', label: 'Error' },
        'info': { severity: 'info', label: 'Info' }
      }
    }
  ];

  activityActions: TableAction[] = [
    {
      label: 'View Details',
      icon: 'pi pi-eye',
      command: (rowData) => this.viewActivityDetails(rowData)
    },
    {
      label: 'Export Log',
      icon: 'pi pi-download',
      command: (rowData) => this.exportActivityLog(rowData)
    }
  ];

  recentActivities = [
    {
      id: 1,
      timestamp: new Date('2024-01-15T10:30:00'),
      user: 'John Smith',
      action: 'Student Registration',
      module: 'Student Management',
      status: 'success'
    },
    {
      id: 2,
      timestamp: new Date('2024-01-15T10:25:00'),
      user: 'Sarah Johnson',
      action: 'Grade Update',
      module: 'Academic Records',
      status: 'success'
    },
    {
      id: 3,
      timestamp: new Date('2024-01-15T10:20:00'),
      user: 'Mike Davis',
      action: 'Class Schedule Change',
      module: 'Scheduling',
      status: 'warning'
    },
    {
      id: 4,
      timestamp: new Date('2024-01-15T10:15:00'),
      user: 'Emily Brown',
      action: 'System Backup',
      module: 'System Administration',
      status: 'success'
    },
    {
      id: 5,
      timestamp: new Date('2024-01-15T10:10:00'),
      user: 'David Wilson',
      action: 'Failed Login Attempt',
      module: 'Authentication',
      status: 'error'
    }
  ];

  ngOnInit(): void {
    this.loadDashboardData();
  }

  onPeriodChange(period: TimePeriod): void {
    this.selectedPeriod = period;
    this.loadDashboardData();
  }

  onRefresh(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    this.chartsLoading = true;
    this.tableLoading = true;

    // Simulate API calls
    setTimeout(() => {
      this.chartsLoading = false;
      this.tableLoading = false;
    }, 1500);
  }

  viewActivityDetails(activity: any): void {
    console.log('Viewing details for activity:', activity);
    // Implement view details logic
  }

  exportActivityLog(activity: any): void {
    console.log('Exporting log for activity:', activity);
    // Implement export logic
  }

  onExportActivities(): void {
    console.log('Exporting all activities');
    // Implement export all logic
  }
}
