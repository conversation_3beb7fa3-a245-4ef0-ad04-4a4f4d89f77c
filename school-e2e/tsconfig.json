{"extends": "../tsconfig.base.json", "compilerOptions": {"allowJs": true, "outDir": "../dist/out-tsc", "sourceMap": false, "module": "commonjs", "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "include": ["**/*.ts", "**/*.js", "playwright.config.ts", "src/**/*.spec.ts", "src/**/*.spec.js", "src/**/*.test.ts", "src/**/*.test.js", "src/**/*.d.ts"]}