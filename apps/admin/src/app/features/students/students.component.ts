import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-students',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">Students Management</h1>
        <p class="page-subtitle">Manage student records, enrollment, and academic information</p>
      </div>
      
      <div class="content-section">
        <p>Students management features will be implemented here.</p>
      </div>
    </div>
  `,
  styles: [`
    .page-container {
      padding: 1.5rem;
    }
    
    .page-header {
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #E0E0E0;
    }
    
    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: #212121;
      margin: 0 0 0.5rem 0;
    }
    
    .page-subtitle {
      font-size: 1rem;
      color: #757575;
      margin: 0;
    }
    
    .content-section {
      background: #FFFFFF;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 2rem;
    }
  `]
})
export class StudentsComponent {}
