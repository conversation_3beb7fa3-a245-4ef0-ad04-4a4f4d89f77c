import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MainLayoutComponent, LayoutConfig, SidebarMenuItem } from '@darajat/shared-layout';
import { ThemeService } from '@darajat/shared-services';

@Component({
  imports: [MainLayoutComponent, RouterModule],
  selector: 'app-root',
  templateUrl: './app.html',
  styleUrl: './app.scss',
})
export class App implements OnInit {
  protected title = 'admin';

  layoutConfig: LayoutConfig = {
    title: 'Darajat Admin',
    showSidebar: true,
    sidebarExpanded: false,
    menuItems: [
      {
        id: 'dashboard',
        label: 'Dashboard',
        icon: 'pi-home',
        route: '/dashboard'
      },
      {
        id: 'students',
        label: 'Students',
        icon: 'pi-users',
        route: '/students'
      },
      {
        id: 'teachers',
        label: 'Teachers',
        icon: 'pi-user',
        route: '/teachers'
      },
      {
        id: 'classes',
        label: 'Classes',
        icon: 'pi-book',
        route: '/classes'
      },
      {
        id: 'schedules',
        label: 'Schedules',
        icon: 'pi-calendar',
        route: '/schedules'
      },
      {
        id: 'reports',
        label: 'Reports',
        icon: 'pi-chart-bar',
        route: '/reports'
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: 'pi-cog',
        route: '/settings'
      }
    ],
    user: {
      name: 'Admin User',
      initials: 'AU'
    },
    notifications: {
      count: 3
    }
  };

  constructor(private themeService: ThemeService) {}

  ngOnInit(): void {
    // Initialize theme
    this.themeService.setTheme('vscode-blue');
  }

  onMenuToggle(expanded: boolean): void {
    this.layoutConfig.sidebarExpanded = expanded;
  }

  onSearch(query: string): void {
    console.log('Search query:', query);
    // Implement global search
  }

  onNotificationClick(): void {
    console.log('Notifications clicked');
    // Implement notifications
  }

  onProfileAction(action: string): void {
    console.log('Profile action:', action);
    switch (action) {
      case 'profile':
        // Navigate to profile
        break;
      case 'settings':
        // Navigate to settings
        break;
      case 'logout':
        // Implement logout
        break;
    }
  }

  onMenuItemClick(item: SidebarMenuItem): void {
    console.log('Menu item clicked:', item);
    // Handle menu item clicks
  }
}
