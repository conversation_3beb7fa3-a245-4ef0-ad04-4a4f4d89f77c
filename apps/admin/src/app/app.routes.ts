import { Route } from '@angular/router';

export const appRoutes: Route[] = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },
  {
    path: 'students',
    loadComponent: () => import('./features/students/students.component').then(m => m.StudentsComponent)
  },
  {
    path: 'teachers',
    loadComponent: () => import('./features/teachers/teachers.component').then(m => m.TeachersComponent)
  },
  {
    path: 'classes',
    loadComponent: () => import('./features/classes/classes.component').then(m => m.ClassesComponent)
  },
  {
    path: 'schedules',
    loadComponent: () => import('./features/schedules/schedules.component').then(m => m.SchedulesComponent)
  },
  {
    path: 'reports',
    loadComponent: () => import('./features/reports/reports.component').then(m => m.ReportsComponent)
  },
  {
    path: 'settings',
    loadComponent: () => import('./features/settings/settings.component').then(m => m.SettingsComponent)
  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
