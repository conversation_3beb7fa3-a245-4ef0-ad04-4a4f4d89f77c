{"name": "shared-utils", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "shared-utils/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/angular:ng-packagr-lite", "outputs": ["{workspaceRoot}/dist/{projectRoot}"], "options": {"project": "shared-utils/ng-package.json"}, "configurations": {"production": {"tsConfig": "shared-utils/tsconfig.lib.prod.json"}, "development": {"tsConfig": "shared-utils/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "shared-utils/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}