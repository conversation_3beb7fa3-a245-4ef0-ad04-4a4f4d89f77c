{"name": "school", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "school/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/school", "browser": "school/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "school/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "school/public"}], "styles": ["school/src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "school:build:production"}, "development": {"buildTarget": "school:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "school:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "school/jest.config.ts"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "school:build", "port": 4200, "staticFilePath": "dist/school/browser", "spa": true}}}}